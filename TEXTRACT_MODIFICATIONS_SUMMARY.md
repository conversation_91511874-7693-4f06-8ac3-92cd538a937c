# Textract Utility Modifications Summary

## Overview
Modified `app/utils/textract.py` to improve functionality, error handling, API design, and logging based on the provided inspiration code and additional requirements.

## Key Changes Made

### 1. S3 URI Support
- **Before**: Required separate `bucket_name` and `object_key` parameters
- **After**: Accepts S3 URI in format `s3://bucket-name/object-key`

#### New Functions Added:
```python
def parse_s3_uri(s3_uri: str) -> Tuple[str, str]:
    """Parse S3 URI into bucket name and object key."""
```

```python
async def process_document_from_s3_uri(self, s3_uri: str) -> Dict[str, Any]:
    """Process document using S3 URI directly."""
```

#### Updated Main Function:
```python
# Before
async def process_document_with_textract(bucket_name: str, object_key: str, ...)

# After  
async def process_document_with_textract(s3_uri: str, ...)
```

### 2. Enhanced Error Handling with Traceback
Added `traceback.format_exc()` to all error logging blocks for better debugging:

```python
except Exception as e:
    logger.error(f"❌ Error: {str(e)}")
    logger.error(f"   Traceback: {traceback.format_exc()}")
    raise
```

**Locations where traceback was added:**
- TextractProcessor initialization
- File metadata retrieval
- Sync Textract analysis
- Async Textract analysis  
- Text extraction
- Main processing method
- Sync/async decision logic

### 3. Improved Sync/Async Decision Logic
Inspired by the reference implementation, enhanced the conditions for choosing sync vs async processing:

#### New Criteria:
- **Supported formats**: `.pdf`, `.jpg`, `.jpeg`, `.png`, `.tiff`, `.tif`
- **File size limit**: 10 MB (increased from 5 MB)
- **Better error messages**: More descriptive reasons for decisions

#### Enhanced Method:
```python
def _should_use_sync_method(self, bucket_name: str, object_key: str) -> Tuple[bool, str]:
    """
    Returns: (can_use_sync, reason)
    
    Criteria:
    - File extension is supported
    - File size is under 10 MB limit  
    - For PDF/TIFF, assumes single page for sync processing
    """
```

### 4. Improved Logging and Metadata
- Added supported formats and file size limits to initialization logging
- Enhanced decision logging with detailed file information
- Better error context in all failure scenarios

## Usage Examples

### Before (Old API):
```python
result = await process_document_with_textract(
    'my-bucket',                    # bucket_name
    'documents/invoice.pdf'         # object_key
)
```

### After (New API):
```python
result = await process_document_with_textract(
    's3://my-bucket/documents/invoice.pdf'  # s3_uri
)
```

### 4. PDF Page Counting (New Feature)
Added actual PDF page counting instead of assuming single page:

```python
def _count_pdf_pages(self, bucket_name: str, object_key: str) -> int:
    """Count pages in a PDF file from S3 using PyPDF2."""
```

**Features:**
- Uses PyPDF2 library to count actual PDF pages
- Downloads PDF content from S3 and analyzes it
- Falls back gracefully if PyPDF2 is not available
- Only allows sync processing for single-page PDFs

### 5. Improved Logging Format
- **File sizes**: Show only in MB format (removed bytes display)
- **S3 location**: Printed once at the proper location (not repeated)
- **Character count**: Show only detected characters, not blocks/lines/words
- **Cleaner output**: Removed redundant timestamp and location logging

### 6. Enhanced Decision Logic
Updated sync/async criteria:
- **PDF files**: Must be single page (calculated, not assumed)
- **TIFF files**: Single page assumed (can be enhanced later)
- **Other formats**: JPG, PNG supported for sync if under 10MB
- **Better reasons**: More descriptive explanations for decisions

## Benefits

1. **Simplified API**: Single S3 URI parameter instead of two separate parameters
2. **Better Debugging**: Comprehensive traceback information in all error scenarios
3. **Smarter Processing**: Actual PDF page counting for accurate sync/async decisions
4. **Enhanced Logging**: Cleaner, more focused logging with relevant information only
5. **Error Resilience**: Better error handling and fallback mechanisms
6. **Accurate Processing**: No more assumptions about PDF pages - actual counting implemented

## Backward Compatibility

The core `TextractProcessor` class methods still accept `bucket_name` and `object_key` parameters, so existing code using the class directly will continue to work. Only the main convenience function `process_document_with_textract()` signature has changed to use S3 URI.

## Dependencies Added

- **PyPDF2**: Added for PDF page counting functionality
  ```bash
  pip install PyPDF2
  ```

## Testing

All modifications have been tested with:
- ✅ S3 URI parsing (valid and invalid URIs)
- ✅ Sync/async decision logic with various file types and sizes
- ✅ PDF page counting (single vs multi-page PDFs)
- ✅ Improved logging format (MB only, character count, single S3 location)
- ✅ Traceback functionality in error scenarios
- ✅ Function signature compatibility

## Example Output

### Before (Old Logging):
```
📄 Starting SYNC Textract document analysis...
   S3 Location: s3://bucket/file.pdf
   Timestamp: 2025-08-27T15:30:00
✅ SYNC Textract analysis completed successfully!
   Blocks detected: 150
✅ Text extraction completed
   Total blocks processed: 150
   Text lines extracted: 45
   Total characters: 1250
```

### After (New Logging):
```
📋 Analyzing file for sync/async decision: s3://bucket/file.pdf
   PDF page count: 1
   PDF is single page, suitable for sync processing
   Size: 2.50 MB
   Format: .pdf (supported)
   Decision: SYNC - File suitable for synchronous processing
🎯 Starting Textract processing: s3://bucket/file.pdf
📄 Starting SYNC Textract document analysis...
✅ SYNC Textract analysis completed successfully!
📝 Extracting text from Textract results...
✅ Text extraction completed
   Characters detected: 1250
✅ Textract processing completed successfully!
   Processing method: SYNC
   Total processing time: 2.34 seconds
```
